version: '3.8'

services:
  website-monitor:
    build: .
    container_name: website-monitor
    restart: unless-stopped
    environment:
      - NODE_ENV=production
    volumes:
      # Mount data directory to persist seen items
      - ./data:/usr/src/app/data
      # Mount logs directory
      - ./logs:/usr/src/app/logs
      # Mount configuration files
      - ./.env:/usr/src/app/.env:ro
      - ./urls.json:/usr/src/app/urls.json:ro
    # Add shared memory size for Chrome
    shm_size: 2gb
    # Security options
    security_opt:
      - seccomp:unconfined
    # Resource limits
    deploy:
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M
    # Health check
    healthcheck:
      test: ["CMD", "node", "-e", "console.log('Health check passed')"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
