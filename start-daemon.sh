#!/bin/bash

# Simple daemon script to run the website monitor in background
# Alternative to PM2 for simple deployments

APP_NAME="website-monitor"
APP_DIR=$(pwd)
PID_FILE="$APP_DIR/$APP_NAME.pid"
LOG_FILE="$APP_DIR/logs/app.log"
ERROR_LOG="$APP_DIR/logs/error.log"

# Create logs directory if it doesn't exist
mkdir -p logs

start() {
    if [ -f "$PID_FILE" ] && kill -0 $(cat "$PID_FILE") 2>/dev/null; then
        echo "❌ $APP_NAME is already running (PID: $(cat $PID_FILE))"
        return 1
    fi
    
    echo "🚀 Starting $APP_NAME..."
    
    # Start the application in background
    nohup node index.js >> "$LOG_FILE" 2>> "$ERROR_LOG" &
    
    # Save the PID
    echo $! > "$PID_FILE"
    
    echo "✅ $APP_NAME started (PID: $(cat $PID_FILE))"
    echo "📄 Logs: $LOG_FILE"
    echo "📄 Errors: $ERROR_LOG"
}

stop() {
    if [ ! -f "$PID_FILE" ]; then
        echo "❌ $APP_NAME is not running (no PID file found)"
        return 1
    fi
    
    PID=$(cat "$PID_FILE")
    
    if ! kill -0 "$PID" 2>/dev/null; then
        echo "❌ $APP_NAME is not running (PID $PID not found)"
        rm -f "$PID_FILE"
        return 1
    fi
    
    echo "🛑 Stopping $APP_NAME (PID: $PID)..."
    kill "$PID"
    
    # Wait for process to stop
    for i in {1..10}; do
        if ! kill -0 "$PID" 2>/dev/null; then
            break
        fi
        sleep 1
    done
    
    # Force kill if still running
    if kill -0 "$PID" 2>/dev/null; then
        echo "⚠️ Force killing $APP_NAME..."
        kill -9 "$PID"
    fi
    
    rm -f "$PID_FILE"
    echo "✅ $APP_NAME stopped"
}

status() {
    if [ -f "$PID_FILE" ] && kill -0 $(cat "$PID_FILE") 2>/dev/null; then
        echo "✅ $APP_NAME is running (PID: $(cat $PID_FILE))"
        return 0
    else
        echo "❌ $APP_NAME is not running"
        return 1
    fi
}

restart() {
    stop
    sleep 2
    start
}

logs() {
    if [ -f "$LOG_FILE" ]; then
        tail -f "$LOG_FILE"
    else
        echo "❌ Log file not found: $LOG_FILE"
    fi
}

case "$1" in
    start)
        start
        ;;
    stop)
        stop
        ;;
    restart)
        restart
        ;;
    status)
        status
        ;;
    logs)
        logs
        ;;
    *)
        echo "Usage: $0 {start|stop|restart|status|logs}"
        echo ""
        echo "Commands:"
        echo "  start   - Start the website monitor"
        echo "  stop    - Stop the website monitor"
        echo "  restart - Restart the website monitor"
        echo "  status  - Check if the monitor is running"
        echo "  logs    - View live logs"
        exit 1
        ;;
esac
