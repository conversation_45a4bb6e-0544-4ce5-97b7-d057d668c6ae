# Complete Step-by-Step Deployment Guide

This comprehensive guide will walk you through deploying your Website Monitor application to your server from start to finish.

## 📋 Prerequisites Checklist

Before starting, ensure you have:

- ✅ **Server access** (SSH credentials)
- ✅ **Node.js 14+** installed on server
- ✅ **Your `.env` file** configured with Slack webhook
- ✅ **Your `urls.json` file** configured with websites to monitor
- ✅ **Server requirements**: 512MB RAM minimum (1GB recommended)
- ✅ **Internet access** on server for outbound HTTPS requests

## 🔍 Step 1: Verify Local Configuration

First, let's make sure everything is ready on your local machine:

```bash
# Check if your configuration is valid
npm run check-config
```

You should see:

```
✅ .env exists: true
✅ urls.json exists: true
✅ Slack webhook configured: true
```

## 🚀 Step 2: Choose Your Deployment Method

Select the method that best fits your server setup:

### **Method A: Simple Server Deployment** (Recommended for beginners)

- Easy to set up and manage
- Uses built-in process management scripts
- Perfect for VPS or dedicated servers

### **Method B: Docker Deployment** (Recommended for containerized environments)

- Isolated environment
- Easy to scale and manage
- Consistent across different servers

### **Method C: Systemd Service** (Recommended for Linux production servers)

- Automatic startup on boot
- System-level process management
- Production-grade reliability

---

## 🔧 Method A: Simple Server Deployment

### Step A1: Copy Files to Your Server

```bash
# Replace with your actual server details
scp -r . user@your-server-ip:/home/<USER>/website-monitor/
```

**Examples:**

```bash
# For root user
scp -r . root@*************:/opt/website-monitor/

# For regular user
scp -r . <EMAIL>:/home/<USER>/website-monitor/

# Using specific SSH key
scp -i ~/.ssh/your-key.pem -r . <EMAIL>:/home/<USER>/website-monitor/
```

### Step A2: Connect to Your Server

```bash
ssh user@your-server-ip
cd /home/<USER>/website-monitor/
```

### Step A3: Run the Deployment Script

```bash
# Make the script executable
chmod +x deploy.sh

# Run the deployment (this will install dependencies and set up the environment)
./deploy.sh
```

**What the deployment script does:**

- ✅ Checks Node.js version compatibility
- ✅ Installs production dependencies
- ✅ Validates your configuration files
- ✅ Creates necessary directories (data/, logs/)
- ✅ Sets proper file permissions
- ✅ Creates systemd service file

### Step A4: Start the Application

```bash
# Make the daemon script executable
chmod +x start-daemon.sh

# Start the website monitor
./start-daemon.sh start
```

### Step A5: Verify It's Working

```bash
# Check if the application is running
./start-daemon.sh status

# View live logs to see activity
./start-daemon.sh logs
```

**Expected output:**

```
🚀 Starting Newsletter Monitor...
📧 Slack notifications enabled
⏰ Check interval: 240 minutes
🌐 Using browser-based monitoring (slower but more reliable)
📋 Monitoring 1 sites:
   • Batdongsan.com.vn: https://batdongsan.com.vn/...
🔍 Running initial check...
✅ Newsletter Monitor is running! Press Ctrl+C to stop.
```

### Step A6: Management Commands

```bash
# Start the monitor
./start-daemon.sh start

# Stop the monitor
./start-daemon.sh stop

# Restart the monitor
./start-daemon.sh restart

# Check status
./start-daemon.sh status

# View live logs
./start-daemon.sh logs
```

---

## 🐳 Method B: Docker Deployment

### Step B1: Ensure Docker is Installed

```bash
# Check if Docker is installed
docker --version
docker-compose --version

# If not installed, install Docker (Ubuntu/Debian example)
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER
```

### Step B2: Copy Files to Server

```bash
# Copy all files to your server
scp -r . user@your-server-ip:/home/<USER>/website-monitor/
```

### Step B3: Connect to Server and Deploy

```bash
# Connect to your server
ssh user@your-server-ip
cd /home/<USER>/website-monitor/

# Build and start the container
docker-compose up -d
```

### Step B4: Verify Docker Deployment

```bash
# Check if container is running
docker-compose ps

# Should show something like:
#     Name                   Command               State    Ports
# website-monitor   docker-entrypoint.sh node ...   Up

# View logs
docker-compose logs -f website-monitor
```

### Step B5: Docker Management Commands

```bash
# View logs
docker-compose logs -f

# Restart container
docker-compose restart

# Stop container
docker-compose down

# Start container
docker-compose up -d

# Update and restart
docker-compose down
docker-compose build --no-cache
docker-compose up -d
```

---

## ⚙️ Method C: Systemd Service (Linux Production)

### Step C1: Copy Files and Initial Setup

```bash
# Copy files to server
scp -r . user@your-server-ip:/home/<USER>/website-monitor/

# Connect to server
ssh user@your-server-ip
cd /home/<USER>/website-monitor/

# Run initial deployment
chmod +x deploy.sh
./deploy.sh
```

### Step C2: Install as System Service

```bash
# Copy the service file to systemd directory
sudo cp website-monitor.service /etc/systemd/system/

# Reload systemd to recognize the new service
sudo systemctl daemon-reload

# Enable service to start on boot
sudo systemctl enable website-monitor

# Start the service
sudo systemctl start website-monitor
```

### Step C3: Verify Systemd Service

```bash
# Check service status
sudo systemctl status website-monitor

# Should show "active (running)" status
```

### Step C4: Systemd Management Commands

```bash
# Check status
sudo systemctl status website-monitor

# View logs
sudo journalctl -u website-monitor -f

# Restart service
sudo systemctl restart website-monitor

# Stop service
sudo systemctl stop website-monitor

# Disable service (won't start on boot)
sudo systemctl disable website-monitor

# View recent logs
sudo journalctl -u website-monitor --since "1 hour ago"
```

---

## 🧪 Step 3: Test Your Deployment

### Verify Slack Integration

1. **Check startup notification**: You should receive a Slack message saying the monitor started
2. **Monitor logs**: Watch for activity messages
3. **Wait for first check**: The app will check your website within 4 hours (or your configured interval)

### Manual Test Commands

```bash
# Test configuration
npm run check-config

# Test browser functionality (Method A only)
npm run test-headless

# Test visible browser (for debugging)
npm run test-visible
```

### Expected Slack Notifications

**Startup Notification:**

```
🚀 Website Monitor Started
The website monitoring service has started successfully!
Started at 12/19/2024, 2:30:45 PM
```

**New Content Found:**

```
🔔 New items on Batdongsan.com.vn
Found 2 new items:
• Cho thuê căn hộ 2PN, Q7 - 6,500,000 VND
• Studio hiện đại gần Lotte - 5,800,000 VND
Checked at 12/19/2024, 6:30:45 PM
```

---

## 🔧 Step 4: Configuration Management

### Environment Variables (.env file)

```env
# Slack webhook URL (required)
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/WEBHOOK/URL

# Check interval in minutes (default: 30, recommended: 240 for 4 hours)
CHECK_INTERVAL_MINUTES=240

# Use browser-based monitoring (recommended for protected sites)
USE_BROWSER=true

# Run browser in headless mode (recommended for servers)
HEADLESS_BROWSER=true

# Log level for debugging
LOG_LEVEL=info

# Save HTML for debugging (optional)
DEBUG_HTML=false
```

### Website Configuration (urls.json)

```json
{
  "site": [
    {
      "name": "Batdongsan.com.vn",
      "url": "https://batdongsan.com.vn/cho-thue-can-ho-chung-cu-quan-7?gtn=5-trieu&gcn=8-trieu"
    },
    {
      "name": "Another Website",
      "url": "https://example.com/listings"
    }
  ]
}
```

---

## 🚨 Step 5: Troubleshooting Common Issues

### Issue 1: "Node.js not found" or Version Too Old

**Problem:** Server doesn't have Node.js or has an old version

**Solution:**

```bash
# Check current version
node --version

# Install Node.js 18 (Ubuntu/Debian)
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install Node.js 18 (CentOS/RHEL)
curl -fsSL https://rpm.nodesource.com/setup_18.x | sudo bash -
sudo yum install -y nodejs

# Verify installation
node --version
npm --version
```

### Issue 2: "Permission denied" Errors

**Problem:** Scripts don't have execute permissions

**Solution:**

```bash
# Make scripts executable
chmod +x deploy.sh start-daemon.sh

# If still having issues, check file ownership
ls -la deploy.sh start-daemon.sh

# Fix ownership if needed
sudo chown $USER:$USER deploy.sh start-daemon.sh
```

### Issue 3: Chrome/Puppeteer Errors

**Problem:** Browser dependencies missing or Chrome crashes

**Solution for Ubuntu/Debian:**

```bash
# Install Chrome dependencies
sudo apt-get update
sudo apt-get install -y \
    gconf-service libasound2 libatk1.0-0 libc6 libcairo2 libcups2 \
    libdbus-1-3 libexpat1 libfontconfig1 libgcc1 libgconf-2-4 \
    libgdk-pixbuf2.0-0 libglib2.0-0 libgtk-3-0 libnspr4 \
    libpango-1.0-0 libpangocairo-1.0-0 libstdc++6 libx11-6 libx11-xcb1 \
    libxcb1 libxcomposite1 libxcursor1 libxdamage1 libxext6 libxfixes3 \
    libxi6 libxrandr2 libxrender1 libxss1 libxtst6 ca-certificates \
    fonts-liberation libappindicator1 libnss3 lsb-release xdg-utils wget

# Install Google Chrome
wget -q -O - https://dl.google.com/linux/linux_signing_key.pub | sudo apt-key add -
sudo sh -c 'echo "deb [arch=amd64] http://dl.google.com/linux/chrome/deb/ stable main" >> /etc/apt/sources.list.d/google.list'
sudo apt-get update
sudo apt-get install -y google-chrome-stable
```

**Solution for CentOS/RHEL:**

```bash
# Install Chrome dependencies
sudo yum install -y \
    alsa-lib atk cups-libs gtk3 libXcomposite libXcursor libXdamage \
    libXext libXi libXrandr libXScrnSaver libXtst pango at-spi2-atk \
    libXt xorg-x11-server-Xvfb

# Install Google Chrome
sudo yum install -y wget
wget https://dl.google.com/linux/direct/google-chrome-stable_current_x86_64.rpm
sudo yum localinstall -y google-chrome-stable_current_x86_64.rpm
```

### Issue 4: "No items found" or Website Not Loading

**Problem:** Website is blocking requests or structure changed

**Solutions:**

```bash
# 1. Test if website is accessible
curl -I "https://batdongsan.com.vn"

# 2. Try with browser mode enabled in .env
USE_BROWSER=true
HEADLESS_BROWSER=true

# 3. Test browser functionality
npm run test-headless

# 4. Enable debug mode to see what's happening
DEBUG_HTML=true
LOG_LEVEL=debug

# 5. Check if website loads in regular browser first
```

### Issue 5: High Memory Usage

**Problem:** Application consuming too much RAM

**Solutions:**

```bash
# 1. Monitor memory usage
free -h
top -p $(pgrep -f "node index.js")

# 2. Reduce check frequency in .env
CHECK_INTERVAL_MINUTES=480  # Check every 8 hours instead of 4

# 3. Use HTTP mode instead of browser mode (if website allows)
USE_BROWSER=false

# 4. Restart the application periodically (add to crontab)
# 0 2 * * * /path/to/website-monitor/start-daemon.sh restart
```

### Issue 6: Slack Notifications Not Working

**Problem:** Not receiving Slack messages

**Solutions:**

```bash
# 1. Verify webhook URL is correct
npm run check-config

# 2. Test webhook manually
curl -X POST -H 'Content-type: application/json' \
    --data '{"text":"Test message from server"}' \
    YOUR_SLACK_WEBHOOK_URL

# 3. Check if .env file is properly formatted (no extra spaces)
cat .env | grep SLACK_WEBHOOK_URL

# 4. Verify Slack app permissions in your workspace
```

### Issue 7: Application Stops Running

**Problem:** Process dies unexpectedly

**Solutions:**

```bash
# 1. Check logs for errors
./start-daemon.sh logs
# or for systemd
sudo journalctl -u website-monitor --since "1 hour ago"

# 2. Use systemd for automatic restart (Method C)
sudo systemctl enable website-monitor

# 3. Add monitoring script to crontab
# */5 * * * * /path/to/website-monitor/start-daemon.sh status || /path/to/website-monitor/start-daemon.sh start

# 4. Check system resources
df -h  # Disk space
free -h  # Memory
```

---

## 📊 Step 6: Monitoring and Maintenance

### Log Locations

**Method A (Simple Deployment):**

- Application logs: `logs/app.log`
- Error logs: `logs/error.log`
- View: `./start-daemon.sh logs`

**Method B (Docker):**

- Container logs: `docker-compose logs -f`
- Persistent logs: Mount volume to `/usr/src/app/logs`

**Method C (Systemd):**

- System logs: `sudo journalctl -u website-monitor -f`
- Application logs: Same as Method A

### Health Monitoring

```bash
# Create a simple health check script
cat > health-check.sh << 'EOF'
#!/bin/bash
if ./start-daemon.sh status > /dev/null 2>&1; then
    echo "✅ Website Monitor is running"
    exit 0
else
    echo "❌ Website Monitor is not running"
    echo "🔄 Attempting to restart..."
    ./start-daemon.sh start
    exit 1
fi
EOF

chmod +x health-check.sh

# Add to crontab for automatic monitoring
# */10 * * * * /path/to/website-monitor/health-check.sh
```

### Backup Important Data

```bash
# Create backup script
cat > backup.sh << 'EOF'
#!/bin/bash
BACKUP_DIR="/backup/website-monitor"
DATE=$(date +%Y%m%d_%H%M%S)

mkdir -p $BACKUP_DIR
tar -czf "$BACKUP_DIR/website-monitor-$DATE.tar.gz" \
    .env urls.json data/ logs/

echo "✅ Backup created: $BACKUP_DIR/website-monitor-$DATE.tar.gz"

# Keep only last 7 backups
find $BACKUP_DIR -name "website-monitor-*.tar.gz" -mtime +7 -delete
EOF

chmod +x backup.sh

# Run backup weekly
# 0 2 * * 0 /path/to/website-monitor/backup.sh
```

---

## 🎯 Step 7: Expected Results and Success Indicators

### What Should Happen After Successful Deployment

1. **Immediate (within 1 minute):**

   - ✅ Slack startup notification received
   - ✅ Application logs show "Newsletter Monitor is running!"
   - ✅ Process status shows as "running"

2. **Within first check interval (4 hours by default):**

   - ✅ Logs show "Running scheduled check"
   - ✅ Website access attempts logged
   - ✅ Either "Found X new items" or "No new items found"

3. **Ongoing operation:**
   - ✅ Regular check messages every 4 hours
   - ✅ Slack notifications when new content is found
   - ✅ Error notifications if website becomes inaccessible

### Sample Success Logs

```
🚀 Starting Newsletter Monitor...
📧 Slack notifications enabled
⏰ Check interval: 240 minutes
🌐 Using browser-based monitoring (slower but more reliable)
🚀 Startup notification sent
📋 Monitoring 1 sites:
   • Batdongsan.com.vn: https://batdongsan.com.vn/...

🔍 Running initial check...
🌐 Starting browser from random location...
👤 Human from random country navigating to: https://batdongsan.com.vn/...
✅ Found 3 new items on Batdongsan.com.vn
🆕 Slack notification sent for 3 new items

⏰ Scheduling checks every 240 minutes
✅ Newsletter Monitor is running! Press Ctrl+C to stop.
```

---

## 📚 Quick Reference Commands

### Essential Commands by Method

**Method A (Simple):**

```bash
./start-daemon.sh start    # Start
./start-daemon.sh stop     # Stop
./start-daemon.sh status   # Check status
./start-daemon.sh logs     # View logs
./start-daemon.sh restart  # Restart
```

**Method B (Docker):**

```bash
docker-compose up -d       # Start
docker-compose down        # Stop
docker-compose ps          # Check status
docker-compose logs -f     # View logs
docker-compose restart     # Restart
```

**Method C (Systemd):**

```bash
sudo systemctl start website-monitor    # Start
sudo systemctl stop website-monitor     # Stop
sudo systemctl status website-monitor   # Check status
sudo journalctl -u website-monitor -f   # View logs
sudo systemctl restart website-monitor  # Restart
```

### Configuration Files

- **`.env`** - Environment variables (Slack webhook, intervals)
- **`urls.json`** - Websites to monitor
- **`data/`** - Stored seen items (auto-created)
- **`logs/`** - Application logs (auto-created)

---

## 🎉 Congratulations!

If you've followed this guide successfully, your Website Monitor is now:

- ✅ **Deployed and running** on your server
- ✅ **Monitoring your configured websites** every 4 hours
- ✅ **Sending Slack notifications** when new content is found
- ✅ **Automatically handling** anti-bot protection and Cloudflare challenges
- ✅ **Logging all activity** for monitoring and debugging
- ✅ **Ready for production use** with proper error handling

Your rental property monitoring system is now active and will help you stay updated on new listings in District 7, Ho Chi Minh City!

**Need help?** Check the troubleshooting section above or review the logs for specific error messages.
