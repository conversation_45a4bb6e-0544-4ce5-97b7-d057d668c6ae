# Deployment Guide

This guide covers different ways to deploy your Website Monitor application to a server.

## Prerequisites

- Server with Node.js 14+ installed
- Your `.env` file configured with Slack webhook
- Your `urls.json` file configured with websites to monitor

## Deployment Options

### Option 1: Direct Node.js Deployment

1. **Copy files to server:**
   ```bash
   scp -r . user@your-server:/path/to/website-monitor/
   ```

2. **On the server, run the deployment script:**
   ```bash
   cd /path/to/website-monitor/
   chmod +x deploy.sh
   ./deploy.sh
   ```

3. **Start the application:**
   ```bash
   # Option A: Run directly
   npm run prod

   # Option B: Run as daemon
   chmod +x start-daemon.sh
   ./start-daemon.sh start
   ```

### Option 2: Systemd Service (Linux)

1. **After running deploy.sh, install the service:**
   ```bash
   sudo cp website-monitor.service /etc/systemd/system/
   sudo systemctl daemon-reload
   sudo systemctl enable website-monitor
   sudo systemctl start website-monitor
   ```

2. **Manage the service:**
   ```bash
   # Check status
   sudo systemctl status website-monitor
   
   # View logs
   sudo journalctl -u website-monitor -f
   
   # Restart
   sudo systemctl restart website-monitor
   ```

### Option 3: Docker Deployment

1. **Build and run with Docker Compose:**
   ```bash
   docker-compose up -d
   ```

2. **Manage the container:**
   ```bash
   # View logs
   docker-compose logs -f
   
   # Restart
   docker-compose restart
   
   # Stop
   docker-compose down
   ```

### Option 4: PM2 Process Manager

1. **Install PM2:**
   ```bash
   npm install -g pm2
   ```

2. **Start with PM2:**
   ```bash
   pm2 start index.js --name website-monitor
   pm2 save
   pm2 startup
   ```

## Server Requirements

### Minimum Requirements
- **RAM:** 512MB (1GB recommended for browser mode)
- **CPU:** 1 core
- **Storage:** 1GB free space
- **OS:** Linux, macOS, or Windows Server

### For Browser Mode (Puppeteer)
- Additional 200-500MB RAM
- Chrome/Chromium dependencies (handled by deploy.sh)

## Environment Variables

Make sure your `.env` file is properly configured:

```env
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/WEBHOOK/URL
CHECK_INTERVAL_MINUTES=240
USE_BROWSER=true
HEADLESS_BROWSER=true
LOG_LEVEL=info
DEBUG_HTML=false
```

## Monitoring and Logs

### Log Locations
- **Direct deployment:** `logs/app.log` and `logs/error.log`
- **Systemd:** `sudo journalctl -u website-monitor`
- **Docker:** `docker-compose logs`
- **PM2:** `pm2 logs website-monitor`

### Health Checks
- Check if process is running: `./start-daemon.sh status`
- Test configuration: `npm run check-config`
- Test browser functionality: `npm run test-headless`

## Troubleshooting

### Common Issues

1. **Permission denied errors:**
   ```bash
   chmod +x deploy.sh start-daemon.sh
   ```

2. **Chrome/Puppeteer issues on Linux:**
   ```bash
   # Install Chrome dependencies
   sudo apt-get update
   sudo apt-get install -y gconf-service libasound2 libatk1.0-0 libc6 libcairo2 libcups2 libdbus-1-3 libexpat1 libfontconfig1 libgcc1 libgconf-2-4 libgdk-pixbuf2.0-0 libglib2.0-0 libgtk-3-0 libnspr4 libpango-1.0-0 libpangocairo-1.0-0 libstdc++6 libx11-6 libx11-xcb1 libxcb1 libxcomposite1 libxcursor1 libxdamage1 libxext6 libxfixes3 libxi6 libxrandr2 libxrender1 libxss1 libxtst6 ca-certificates fonts-liberation libappindicator1 libnss3 lsb-release xdg-utils wget
   ```

3. **Memory issues:**
   - Reduce check frequency
   - Monitor fewer websites
   - Use HTTP mode instead of browser mode

## Security Considerations

1. **Keep .env file secure:**
   ```bash
   chmod 600 .env
   ```

2. **Run as non-root user:**
   ```bash
   sudo useradd -r -s /bin/false website-monitor
   sudo chown -R website-monitor:website-monitor /path/to/app
   ```

3. **Firewall configuration:**
   - No inbound ports needed (application makes outbound requests only)
   - Ensure outbound HTTPS (443) is allowed

## Backup and Recovery

### Important files to backup:
- `.env` (configuration)
- `urls.json` (monitored websites)
- `data/` directory (seen items history)

### Backup script:
```bash
#!/bin/bash
tar -czf website-monitor-backup-$(date +%Y%m%d).tar.gz .env urls.json data/
```

## Updates

To update the application:

1. **Stop the service:**
   ```bash
   ./start-daemon.sh stop
   # or
   sudo systemctl stop website-monitor
   ```

2. **Update files and restart:**
   ```bash
   npm install
   ./start-daemon.sh start
   # or
   sudo systemctl start website-monitor
   ```
