#!/bin/bash

# Deployment script for Website Monitor
# This script prepares the application for deployment to a server

echo "🚀 Preparing Website Monitor for deployment..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 14+ first."
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 14 ]; then
    echo "❌ Node.js version 14+ is required. Current version: $(node --version)"
    exit 1
fi

echo "✅ Node.js version: $(node --version)"

# Install dependencies
echo "📦 Installing dependencies..."
npm install --production

if [ $? -ne 0 ]; then
    echo "❌ Failed to install dependencies"
    exit 1
fi

# Check configuration
echo "🔍 Checking configuration..."
npm run check-config

if [ $? -ne 0 ]; then
    echo "❌ Configuration check failed"
    exit 1
fi

# Create necessary directories
echo "📁 Creating necessary directories..."
mkdir -p data
mkdir -p logs

# Set proper permissions
echo "🔐 Setting permissions..."
chmod +x index.js
chmod +x deploy.sh

# Create systemd service file (for Linux servers)
echo "⚙️ Creating systemd service file..."
cat > website-monitor.service << EOF
[Unit]
Description=Website Monitor with Slack Notifications
After=network.target

[Service]
Type=simple
User=\$USER
WorkingDirectory=$(pwd)
ExecStart=/usr/bin/node index.js
Restart=always
RestartSec=10
Environment=NODE_ENV=production

# Logging
StandardOutput=append:/var/log/website-monitor.log
StandardError=append:/var/log/website-monitor-error.log

[Install]
WantedBy=multi-user.target
EOF

echo "✅ Deployment preparation complete!"
echo ""
echo "📋 Next steps for server deployment:"
echo "1. Copy this entire directory to your server"
echo "2. Run this script on your server: ./deploy.sh"
echo "3. For systemd service (Linux):"
echo "   sudo cp website-monitor.service /etc/systemd/system/"
echo "   sudo systemctl daemon-reload"
echo "   sudo systemctl enable website-monitor"
echo "   sudo systemctl start website-monitor"
echo ""
echo "4. Or run manually: npm run prod"
echo ""
echo "🔍 Monitor logs with:"
echo "   tail -f logs/app.log"
echo "   sudo journalctl -u website-monitor -f  # For systemd"
